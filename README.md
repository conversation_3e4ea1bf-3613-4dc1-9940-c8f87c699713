# zfmi 转发

一个包含 mihomo 和预配置的 Docker 镜像，支持 AMD64 和 ARM64 架构，使用主机网络模式，一步到位运行代理服务。

## 特性

- ✅ **多架构支持**: 支持 AMD64 和 ARM64 架构
- ✅ **预配置**: 内置配置文件，开箱即用
- ✅ **主机网络**: 使用主机网络模式，无需端口映射
- ✅ **自动构建**: GitHub Actions 自动构建和发布
- ✅ **安全扫描**: 集成 Trivy 安全扫描
- ✅ **健康检查**: 内置健康检查机制
- ✅ **优雅关闭**: 支持信号处理和优雅关闭

## 快速开始

### 系统准备（重要）

在运行Docker容器前，需要准备系统环境以支持TUN模式：

```bash
# 1. 加载TUN模块
sudo modprobe tun

# 2. 创建TUN设备（如果不存在）
sudo mkdir -p /dev/net
sudo mknod /dev/net/tun c 10 200

# 3. 启用IP转发
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward
echo 1 | sudo tee /proc/sys/net/ipv6/conf/all/forwarding
```

### 使用 Docker 运行

#### 基础部署（HTTP）

```bash
# 使用主机网络模式运行（推荐）
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --privileged \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  --cap-add NET_BIND_SERVICE \
  --device /dev/net/tun:/dev/net/tun \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  --volume /lib/modules:/lib/modules:ro \
  --volume /etc/resolv.conf:/etc/resolv.conf:ro \
  jhxxr/zfmi:latest
```

#### 🔒 SSL/HTTPS 加密部署（推荐）

为了确保API通信安全，建议启用SSL加密：

```bash
# 1. 首先生成SSL证书（在宿主机上执行）
mkdir -p ./ssl
docker run --rm -v $(pwd)/ssl:/ssl alpine/openssl req -x509 -newkey rsa:2048 -keyout /ssl/api-server.key -out /ssl/api-server.crt -days 3650 -nodes -subj "/CN=ZFMI-API" -addext "subjectAltName=DNS:localhost,IP:127.0.0.1,IP:$(hostname -I | awk '{print $1}')"

# 2. 设置证书权限
chmod 600 ./ssl/api-server.key
chmod 644 ./ssl/api-server.crt

# 3. 启动支持HTTPS的容器
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --privileged \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  --cap-add NET_BIND_SERVICE \
  --device /dev/net/tun:/dev/net/tun \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  --env ENABLE_SSL=true \
  --env SSL_PORT=8443 \
  --env ENABLE_HTTP=true \
  --volume /lib/modules:/lib/modules:ro \
  --volume /etc/resolv.conf:/etc/resolv.conf:ro \
  --volume $(pwd)/ssl:/app/ssl:ro \
  jhxxr/zfmi:latest
```

#### 🛡️ 仅HTTPS模式（最安全）

如果只想使用HTTPS，禁用HTTP：

```bash
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --privileged \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  --cap-add NET_BIND_SERVICE \
  --device /dev/net/tun:/dev/net/tun \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  --env ENABLE_SSL=true \
  --env SSL_PORT=8443 \
  --env ENABLE_HTTP=false \
  --volume /lib/modules:/lib/modules:ro \
  --volume /etc/resolv.conf:/etc/resolv.conf:ro \
  --volume $(pwd)/ssl:/app/ssl:ro \
  jhxxr/zfmi:latest
```

#### 容器管理命令

```bash
# 查看日志
docker logs -f zfmi

# 停止容器
docker stop zfmi

# 重启容器
docker restart zfmi

# 删除容器
docker rm zfmi
```

### 使用 Docker Compose

#### 基础配置

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  zfmi:
    image: jhxxr/zfmi:latest
    container_name: zfmi
    network_mode: host
    restart: unless-stopped
    privileged: true
    cap_add:
      - NET_ADMIN
      - NET_RAW
      - NET_BIND_SERVICE
    devices:
      - /dev/net/tun:/dev/net/tun
    environment:
      - TZ=Asia/Shanghai
      - API_SECRET=jhxnb666
      - MIHOMO_SECRET=jhxnb666
      - SUPERVISOR_ENABLED=true
      - MIHOMO_LOG_LEVEL=info
      - LOG_LEVEL=info
    volumes:
      - /lib/modules:/lib/modules:ro
      - /etc/resolv.conf:/etc/resolv.conf:ro
      - ./logs:/var/log/mihomo  # 可选：持久化日志
```

#### 🔒 SSL/HTTPS 配置

支持SSL加密的 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  zfmi:
    image: jhxxr/zfmi:latest
    container_name: zfmi
    network_mode: host
    restart: unless-stopped
    privileged: true
    cap_add:
      - NET_ADMIN
      - NET_RAW
      - NET_BIND_SERVICE
    devices:
      - /dev/net/tun:/dev/net/tun
    environment:
      - TZ=Asia/Shanghai
      - API_SECRET=jhxnb666
      - MIHOMO_SECRET=jhxnb666
      - SUPERVISOR_ENABLED=true
      - MIHOMO_LOG_LEVEL=info
      - LOG_LEVEL=info
      # SSL配置
      - ENABLE_SSL=true
      - SSL_PORT=8443
      - ENABLE_HTTP=true
    volumes:
      - /lib/modules:/lib/modules:ro
      - /etc/resolv.conf:/etc/resolv.conf:ro
      - ./ssl:/app/ssl:ro  # SSL证书挂载
      - ./logs:/var/log/mihomo
```

运行：

```bash
# 生成SSL证书（首次运行）
mkdir -p ./ssl
docker run --rm -v $(pwd)/ssl:/ssl alpine/openssl req -x509 -newkey rsa:2048 -keyout /ssl/api-server.key -out /ssl/api-server.crt -days 3650 -nodes -subj "/CN=ZFMI-API" -addext "subjectAltName=DNS:localhost,IP:127.0.0.1,IP:$(hostname -I | awk '{print $1}')"
chmod 600 ./ssl/api-server.key
chmod 644 ./ssl/api-server.crt

# 启动服务
docker-compose up -d
```

### 🚀 智能部署脚本（推荐）

为了简化部署过程，我们提供了智能部署脚本 `smart_deploy.sh`，它可以自动处理SSL证书生成和容器部署：

```bash
# 下载脚本
curl -O https://raw.githubusercontent.com/your-repo/zfmi/main/smart_deploy.sh
chmod +x smart_deploy.sh

# 基础部署（仅HTTP）
./smart_deploy.sh

# 启用SSL并自动生成证书
./smart_deploy.sh --ssl --generate-ssl

# 仅HTTPS模式（最安全）
./smart_deploy.sh --https-only --generate-ssl

# 强制重新创建容器
./smart_deploy.sh --force

# 自定义配置
./smart_deploy.sh --ssl --generate-ssl --api-key "your-secret" --name "my-zfmi"
```

#### 脚本选项说明

| 选项 | 说明 |
|------|------|
| `-h, --help` | 显示帮助信息 |
| `-s, --ssl` | 启用SSL/HTTPS |
| `-g, --generate-ssl` | 自动生成SSL证书 |
| `-o, --https-only` | 仅启用HTTPS（禁用HTTP） |
| `-f, --force` | 强制重新创建容器 |
| `-n, --name NAME` | 自定义容器名称 |
| `-k, --api-key KEY` | 自定义API密钥 |
| `-m, --mihomo-key KEY` | 自定义mihomo密钥 |
| `--ssl-port PORT` | 自定义HTTPS端口 |
| `--http-port PORT` | 自定义HTTP端口 |

## 端口说明

由于使用主机网络模式，以下端口将直接在主机上监听：

### mihomo 核心端口
- `10801`: Mixed Port (HTTP/SOCKS5 代理端口)
- `11024`: External Controller (RESTful API 端口)
- `12790`: 隧道转发端口

### API 服务端口
- `8888`: HTTP API 端口（默认）
- `8443`: HTTPS API 端口（启用SSL时）

### 访问地址
- **HTTP API**: `http://your-ip:8888/api/`
- **HTTPS API**: `https://your-ip:8443/api/` （需要接受自签名证书）
- **mihomo API**: `http://your-ip:11024/`

## 配置说明

### 默认配置

镜像内置了以下配置：

#### mihomo 配置
- **Mixed Port**: 10801 (HTTP/SOCKS5 代理)
- **External Controller**: 0.0.0.0:11024
- **mihomo API 密钥**: `jhxnb666`
- **TUN 模式**: 启用
- **日志级别**: info

#### API 服务配置
- **HTTP 端口**: 8888
- **HTTPS 端口**: 8443 (需启用SSL)
- **API 密钥**: `jhxnb666`
- **SSL**: 默认禁用
- **同时启用HTTP**: 默认启用

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `API_SECRET` | `jhxnb666` | API访问密钥 |
| `MIHOMO_SECRET` | `jhxnb666` | mihomo API密钥 |
| `ENABLE_SSL` | `false` | 是否启用SSL/HTTPS |
| `SSL_PORT` | `8443` | HTTPS监听端口 |
| `ENABLE_HTTP` | `true` | 是否同时启用HTTP |
| `LOG_LEVEL` | `info` | API服务日志级别 |
| `MIHOMO_LOG_LEVEL` | `info` | mihomo日志级别 |
| `SUPERVISOR_ENABLED` | `true` | 是否使用supervisor管理进程 |


### 更新

```bash
# 删除未使用的镜像
docker rmi $(docker images -f "dangling=true" -q)
# 拉取镜像
docker pull jhxxr/zfmi:latest
# 重启容器  
docker pull jhxxr/zfmi:latest
docker stop zfmi
docker rm zfmi
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --privileged \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  --cap-add NET_BIND_SERVICE \
  --device /dev/net/tun:/dev/net/tun \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  --volume /lib/modules:/lib/modules:ro \
  --volume /etc/resolv.conf:/etc/resolv.conf:ro \
  jhxxr/zfmi:latest
```
# 查看日志
docker logs -f zfmi

## 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MIHOMO_LOG_LEVEL` | `info` | 日志级别 (debug, info, warning, error) |
| `MIHOMO_CONFIG_DIR` | `/etc/mihomo` | 配置文件目录 |

## 构建说明

### 本地构建

```bash
# 克隆仓库
git clone https://github.com/your-username/mihomo.git
cd mihomo

# 构建镜像
./build.sh --name mihomo --tag latest

# 构建并推送到 Docker Hub
./build.sh --name mihomo --tag latest --registry your-username/ --push
```

### GitHub Actions 自动构建

项目配置了 GitHub Actions 自动构建：

1. **推送到主分支**: 自动构建并推送 `latest` 标签
2. **创建 Release**: 自动构建并推送版本标签
3. **手动触发**: 可以手动触发构建指定版本

#### 设置 Secrets

在 GitHub 仓库设置中添加以下 Secrets：

- `DOCKER_USERNAME`: Docker Hub 用户名
- `DOCKER_PASSWORD`: Docker Hub 密码或访问令牌

## 使用示例

### 基本代理使用

```bash
# 设置 HTTP 代理
export http_proxy=http://localhost:10801
export https_proxy=http://localhost:10801

# 设置 SOCKS5 代理
export ALL_PROXY=socks5://localhost:10801

# 测试连接
curl -I https://www.google.com
```

### API 管理

```bash
# 获取版本信息
curl -H "Authorization: Bearer jhxnb666" http://localhost:11024/version

# 获取代理信息
curl -H "Authorization: Bearer jhxnb666" http://localhost:11024/proxies

# 切换代理
curl -X PUT -H "Authorization: Bearer jhxnb666" \
  -H "Content-Type: application/json" \
  -d '{"name":"🇭🇰 [直连]香港anytls[xxc][新协议]"}' \
  http://localhost:11024/proxies/ALL-PROXY
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker logs mihomo
   
   # 检查配置文件
   docker exec mihomo cat /etc/mihomo/config.yaml
   ```

2. **网络连接问题**
   ```bash
   # 检查端口监听
   netstat -tlnp | grep -E "(10801|11024|12790)"
   
   # 测试 API 连接
   curl http://localhost:11024/version
   ```

3. **权限问题**
   - 确保容器有 `NET_ADMIN` 权限
   - 检查 TUN 设备是否可用



## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 🔒 SSL/HTTPS 配置详解

### 为什么需要SSL？

在使用IP地址访问API时，HTTP通信是明文传输，存在安全风险。启用SSL可以：

- 🔐 **加密通信**: 防止API密钥和数据被窃听
- 🛡️ **身份验证**: 确保连接到正确的服务器
- 🚫 **防止篡改**: 保护数据传输完整性

### SSL证书管理

#### 自动生成证书（推荐）

```bash
# 使用智能部署脚本自动生成
./smart_deploy.sh --ssl --generate-ssl

# 或手动生成
bash scripts/generate_ssl_cert.sh
```

#### 手动生成证书

```bash
# 创建证书目录
mkdir -p ./ssl

# 生成私钥和证书
openssl req -x509 -newkey rsa:2048 -keyout ./ssl/api-server.key -out ./ssl/api-server.crt -days 3650 -nodes -subj "/CN=ZFMI-API" -addext "subjectAltName=DNS:localhost,IP:127.0.0.1,IP:$(hostname -I | awk '{print $1}')"

# 设置权限
chmod 600 ./ssl/api-server.key
chmod 644 ./ssl/api-server.crt
```

### 浏览器证书信任

由于使用自签名证书，首次访问时浏览器会显示安全警告：

#### Chrome/Edge
1. 点击"高级"
2. 点击"继续前往 xxx.xxx.xxx.xxx（不安全）"
3. 或将证书添加到系统信任列表

#### Firefox
1. 点击"高级"
2. 点击"接受风险并继续"

#### Safari
1. 点击"显示详细信息"
2. 点击"访问此网站"

### SSL配置选项

| 环境变量 | 默认值 | 说明 |
|----------|--------|------|
| `ENABLE_SSL` | `false` | 是否启用SSL |
| `SSL_PORT` | `8443` | HTTPS端口 |
| `ENABLE_HTTP` | `true` | 是否同时启用HTTP |
| `SSL_CERT_FILE` | `/app/ssl/api-server.crt` | 证书文件路径 |
| `SSL_KEY_FILE` | `/app/ssl/api-server.key` | 私钥文件路径 |

### 安全建议

1. **定期更新证书**: 虽然生成的证书有效期10年，建议定期更新
2. **保护私钥**: 确保私钥文件权限为600，只有owner可读写
3. **使用强密码**: 设置复杂的API密钥
4. **网络隔离**: 在生产环境中使用防火墙限制访问
5. **监控日志**: 定期检查访问日志，发现异常访问

## 相关链接

- [mihomo 官方仓库](https://github.com/MetaCubeX/mihomo)
- [Docker Hub](https://hub.docker.com/r/your-username/mihomo)
- [GitHub Actions](https://github.com/your-username/mihomo/actions)
