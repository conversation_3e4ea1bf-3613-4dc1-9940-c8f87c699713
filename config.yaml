# ========== 基础设置 ==========
mixed-port: 10801
allow-lan: true
bind-address: "*"
mode: rule
log-level: debug


external-controller: 0.0.0.0:11024 # RESTful API 监听地址
secret: "jhxnb666" # RESTful API 密钥

# DNS 配置
dns:
  enable: true
  listen: 0.0.0.0:1053
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *********
    - ***************
    - *******
  fallback:
    - *******
    - *******

profile: # 存储 select 选择记录
  store-selected: true

  # 持久化 fake-ip
  store-fake-ip: true

# Tun 配置 - 在Docker环境中禁用TUN模式以避免权限问题
tun:
  enable: true  
  stack: mixed # gvisor/mixed
  dns-hijack:
    - 0.0.0.0:53 # 需要劫持的 DNS
  auto-detect-interface: true # 自动识别出口网卡
  auto-route: true 
  auto-redirect: true 
  mtu: 1500 # 降低MTU值以提高兼容性

# ========== 出站代理节点列表 ==========
proxies:
  - name: "🇭🇰 [直连]香港anytls[xxc][新协议]"
    server: ************** 
    port: 43233
    type: anytls
    password: dc797c09-9520-4ce8-9254-bb8ea3363eb1
    sni: steam.wapis.7878007.xyz
    skip-cert-verify: true
    udp: true
    tfo: false

  - name: "🇭🇰 [直连]香港[new协议][xxc]"
    server: music.hyh.7878007.xyz
    port: 4344
    type: tuic
    uuid: dc797c09-9520-4ce8-9254-bb8ea3363eb1
    password: dc797c09-9520-4ce8-9254-bb8ea3363eb1
    alpn: [h3]
    udp-relay-mode: quic
    congestion-controller: bbr
    sni: steam.wapis.7878007.xyz
    skip-cert-verify: true
    udp: true
    tfo: false

# ========== 代理组 ==========
proxy-groups:
  # 自动测速组（URLTest/health-check）
  - name: auto-test
    type: url-test
    proxies:
      - "🇭🇰 [直连]香港anytls[xxc][新协议]"
      - "🇭🇰 [直连]香港[new协议][xxc]"
    url: https://www.gstatic.com/generate_204
    interval: 600  # 增加检查间隔以减少频繁检查
    timeout: 5000  # 设置超时时间为5秒
    tolerance: 50  # 设置延迟容忍度

  # 手动选择组（Select）
  - name: manual-select
    type: select
    proxies:
      - "🇭🇰 [直连]香港anytls[xxc][新协议]"
      - "🇭🇰 [直连]香港[new协议][xxc]"

  # 负载均衡组（轮询/Load-Balance）
  - name: load-balance
    type: load-balance
    strategy: round-robin
    proxies:
      - "🇭🇰 [直连]香港anytls[xxc][新协议]"
      - "🇭🇰 [直连]香港[new协议][xxc]"

  # 汇总组：将上面三个组作为“出站标签”
  - name: ALL-PROXY
    type: select
    proxies:
      - auto-test
      - manual-select
      - load-balance

# ========== 隧道转发 ==========
tunnels:
  - network: [tcp, udp]
    address: 0.0.0.0:12790
    target: **************:4333
    proxy: ALL-PROXY

# ========== 其他部分保持官方模板 ==========
# mixed-port、dns、routing、external-controller 等按需填充 :contentReference[oaicite:0]{index=0}
