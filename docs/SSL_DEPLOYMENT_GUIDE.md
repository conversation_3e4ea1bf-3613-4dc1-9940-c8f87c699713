# 🔒 ZFMI SSL部署指南

## 概述

本指南详细说明如何为ZFMI项目配置SSL加密通信，包括API服务器和Web前端的SSL支持。

## 🚀 快速开始

### 1. 使用智能部署脚本（推荐）

```bash
# 生成SSL证书并启用HTTPS
./smart_deploy.sh --ssl --generate-ssl

# 仅启用HTTPS（需要已有证书）
./smart_deploy.sh --https-only

# 同时支持HTTP和HTTPS
./smart_deploy.sh --ssl
```

### 2. 手动部署

#### 步骤1: 生成SSL证书

```bash
# 运行证书生成脚本
./scripts/generate_ssl_cert.sh

# 或手动生成
mkdir -p ssl
openssl req -newkey rsa:2048 -nodes -keyout ssl/api-server.key \
    -x509 -days 365 -out ssl/api-server.crt \
    -subj "/C=CN/ST=State/L=City/O=Organization/CN=*************"
```

#### 步骤2: 配置环境变量

创建或更新 `.env` 文件：

```env
# SSL配置
ENABLE_SSL=true
SSL_CERT_FILE=./ssl/api-server.crt
SSL_KEY_FILE=./ssl/api-server.key
SSL_PORT=8443

# 可选：同时启用HTTP
ENABLE_HTTP=true
HTTP_PORT=8888
```

#### 步骤3: 启动服务

```bash
# Docker部署
docker run -d \
  --name zfmi-api \
  -p 8888:8888 \
  -p 8443:8443 \
  -v $(pwd)/ssl:/app/ssl:ro \
  -v $(pwd)/.env:/app/.env:ro \
  your-registry/zfmi-api:latest

# 或直接运行
python api/main.py
```

## 🌐 Web前端SSL配置

### 新功能特性

1. **协议选择**: 支持HTTP/HTTPS协议切换
2. **智能端口**: 根据协议自动调整默认端口
3. **连接测试**: 内置SSL连接测试功能
4. **证书处理**: 支持自签名证书的忽略选项

### 使用方法

1. 打开Web管理面板
2. 进入"系统设置"页面
3. 在"连接设置"中：
   - 选择协议类型（HTTP/HTTPS）
   - 输入服务器地址和端口
   - 如使用自签名证书，勾选"忽略SSL证书错误"
   - 点击"测试连接"验证配置
   - 保存设置

### SSL测试工具

项目包含独立的SSL测试工具：`web/ssl-test.html`

功能：
- 基本连接测试
- 健康检查接口测试
- 状态接口测试
- SSL证书错误诊断

## 🔧 配置选项

### API服务器配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `ENABLE_SSL` | `false` | 启用SSL/HTTPS |
| `SSL_CERT_FILE` | `./ssl/api-server.crt` | SSL证书文件路径 |
| `SSL_KEY_FILE` | `./ssl/api-server.key` | SSL私钥文件路径 |
| `SSL_PORT` | `8443` | HTTPS端口 |
| `ENABLE_HTTP` | `true` | 同时启用HTTP |
| `HTTP_PORT` | `8888` | HTTP端口 |

### Web前端配置

Web前端会自动保存以下设置到localStorage：

- `mihomo_protocol`: 协议类型
- `mihomo_api_host`: 服务器地址
- `mihomo_api_port`: 端口号
- `mihomo_api_url`: 完整API地址
- `mihomo_api_token`: API令牌
- `mihomo_ignore_ssl_errors`: 是否忽略SSL错误

## 🛡️ 安全建议

### 生产环境

1. **使用有效证书**: 避免使用自签名证书
2. **证书管理**: 定期更新SSL证书
3. **访问控制**: 限制API访问来源
4. **令牌安全**: 使用强密码作为API令牌

### 开发/测试环境

1. **自签名证书**: 可以使用项目提供的证书生成脚本
2. **浏览器信任**: 手动在浏览器中接受自签名证书
3. **忽略SSL错误**: 仅在开发环境中使用

## 🔍 故障排除

### 常见问题

#### 1. SSL证书错误

**症状**: 浏览器显示"不安全连接"或API调用失败

**解决方案**:
- 在浏览器中手动访问API地址并接受证书
- 在Web界面中启用"忽略SSL证书错误"
- 使用有效的SSL证书

#### 2. CORS错误

**症状**: 跨域请求被阻止

**解决方案**:
- 确保API服务器配置了正确的CORS策略
- 检查API服务器是否正常运行

#### 3. 连接超时

**症状**: 连接测试超时

**解决方案**:
- 检查服务器地址和端口是否正确
- 确认防火墙设置允许相应端口访问
- 验证SSL证书配置

### 调试工具

1. **SSL测试页面**: `web/ssl-test.html`
2. **浏览器开发者工具**: 查看网络请求详情
3. **API日志**: 检查服务器端日志输出

## 📚 相关文档

- [README.md](../README.md) - 项目总体说明
- [智能部署脚本](../smart_deploy.sh) - 自动化部署工具
- [SSL证书生成脚本](../scripts/generate_ssl_cert.sh) - 证书生成工具

## 🤝 支持

如遇到问题，请：

1. 查看本指南的故障排除部分
2. 使用SSL测试工具进行诊断
3. 检查API服务器日志
4. 提交Issue并附上详细的错误信息
