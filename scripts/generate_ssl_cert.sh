#!/bin/bash

# SSL证书生成脚本
# 用于生成自签名SSL证书，支持IP地址和localhost访问

set -e

# 配置变量
CERT_DIR="./ssl"
CERT_NAME="api-server"
CERT_FILE="${CERT_DIR}/${CERT_NAME}.crt"
KEY_FILE="${CERT_DIR}/${CERT_NAME}.key"
CONFIG_FILE="${CERT_DIR}/openssl.conf"

# 证书有效期（天）
DAYS=3650

# 获取本机IP地址
get_local_ip() {
    # 尝试多种方法获取本机IP
    local ip=""
    
    # 方法1: 使用ip命令
    if command -v ip >/dev/null 2>&1; then
        ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    fi
    
    # 方法2: 使用ifconfig命令
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    # 方法3: 使用hostname命令
    if [ -z "$ip" ] && command -v hostname >/dev/null 2>&1; then
        ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
    
    # 默认值
    if [ -z "$ip" ]; then
        ip="*************"
        echo "警告: 无法自动获取IP地址，使用默认值: $ip"
    fi
    
    echo "$ip"
}

# 创建证书目录
create_cert_dir() {
    echo "创建证书目录: $CERT_DIR"
    mkdir -p "$CERT_DIR"
}

# 生成OpenSSL配置文件
generate_openssl_config() {
    local local_ip=$(get_local_ip)
    
    echo "生成OpenSSL配置文件: $CONFIG_FILE"
    echo "本机IP地址: $local_ip"
    
    cat > "$CONFIG_FILE" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=CN
ST=Beijing
L=Beijing
O=ZFMI
OU=API Server
CN=ZFMI API Server

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = api.local
DNS.3 = zfmi.local
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = $local_ip
IP.4 = 0.0.0.0
EOF

    # 如果用户提供了额外的IP地址，添加到配置中
    if [ ! -z "$1" ]; then
        echo "IP.5 = $1" >> "$CONFIG_FILE"
        echo "添加额外IP地址: $1"
    fi
}

# 生成私钥
generate_private_key() {
    echo "生成私钥: $KEY_FILE"
    openssl genrsa -out "$KEY_FILE" 2048
    chmod 600 "$KEY_FILE"
}

# 生成证书
generate_certificate() {
    echo "生成证书: $CERT_FILE"
    openssl req -new -x509 -key "$KEY_FILE" -out "$CERT_FILE" -days "$DAYS" -config "$CONFIG_FILE" -extensions v3_req
    chmod 644 "$CERT_FILE"
}

# 显示证书信息
show_certificate_info() {
    echo ""
    echo "========================================="
    echo "SSL证书生成完成！"
    echo "========================================="
    echo "证书文件: $CERT_FILE"
    echo "私钥文件: $KEY_FILE"
    echo "有效期: $DAYS 天"
    echo ""
    echo "证书详细信息:"
    openssl x509 -in "$CERT_FILE" -text -noout | grep -A 10 "Subject Alternative Name"
    echo ""
    echo "证书指纹:"
    openssl x509 -in "$CERT_FILE" -fingerprint -noout
    echo ""
    echo "========================================="
    echo "使用说明:"
    echo "1. 将证书和私钥文件放在API服务器可访问的位置"
    echo "2. 配置API服务器使用HTTPS"
    echo "3. 在浏览器中访问时，需要接受自签名证书警告"
    echo "4. 建议将证书添加到系统信任列表中"
    echo "========================================="
}

# 主函数
main() {
    echo "开始生成SSL证书..."
    echo ""
    
    # 检查OpenSSL是否安装
    if ! command -v openssl >/dev/null 2>&1; then
        echo "错误: 未找到openssl命令，请先安装OpenSSL"
        exit 1
    fi
    
    # 创建目录
    create_cert_dir
    
    # 生成配置文件
    generate_openssl_config "$1"
    
    # 生成私钥
    generate_private_key
    
    # 生成证书
    generate_certificate
    
    # 显示信息
    show_certificate_info
    
    echo "SSL证书生成完成！"
}

# 帮助信息
show_help() {
    echo "用法: $0 [额外IP地址]"
    echo ""
    echo "示例:"
    echo "  $0                    # 使用自动检测的IP地址"
    echo "  $0 *************     # 添加额外的IP地址"
    echo ""
    echo "说明:"
    echo "  此脚本会生成自签名SSL证书，支持以下访问方式:"
    echo "  - localhost"
    echo "  - 127.0.0.1"
    echo "  - 本机IP地址（自动检测）"
    echo "  - 额外指定的IP地址"
    echo ""
}

# 处理命令行参数
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
