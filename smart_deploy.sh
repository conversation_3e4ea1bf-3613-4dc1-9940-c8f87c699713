#!/bin/bash

# ZFMI 智能部署脚本
# 自动处理SSL证书生成和Docker容器部署

set -e

# 默认配置
CONTAINER_NAME="zfmi"
IMAGE_NAME="jhxxr/zfmi:latest"
API_SECRET="jhxnb666"
MIHOMO_SECRET="jhxnb666"
SSL_ENABLED="false"
HTTP_ENABLED="true"
SSL_PORT="8443"
HTTP_PORT="8888"
FORCE_RECREATE="false"
GENERATE_SSL="false"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}         ZFMI 智能部署脚本${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -s, --ssl               启用SSL/HTTPS"
    echo "  -g, --generate-ssl      生成SSL证书"
    echo "  -o, --https-only        仅启用HTTPS（禁用HTTP）"
    echo "  -f, --force             强制重新创建容器"
    echo "  -n, --name NAME         容器名称 (默认: zfmi)"
    echo "  -i, --image IMAGE       镜像名称 (默认: jhxxr/zfmi:latest)"
    echo "  -k, --api-key KEY       API密钥 (默认: jhxnb666)"
    echo "  -m, --mihomo-key KEY    mihomo密钥 (默认: jhxnb666)"
    echo "  --ssl-port PORT         HTTPS端口 (默认: 8443)"
    echo "  --http-port PORT        HTTP端口 (默认: 8888)"
    echo ""
    echo "示例:"
    echo "  $0                      # 基础部署（仅HTTP）"
    echo "  $0 -s -g               # 启用SSL并生成证书"
    echo "  $0 -o -g               # 仅HTTPS模式并生成证书"
    echo "  $0 -f                  # 强制重新创建容器"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--ssl)
                SSL_ENABLED="true"
                shift
                ;;
            -g|--generate-ssl)
                GENERATE_SSL="true"
                shift
                ;;
            -o|--https-only)
                SSL_ENABLED="true"
                HTTP_ENABLED="false"
                shift
                ;;
            -f|--force)
                FORCE_RECREATE="true"
                shift
                ;;
            -n|--name)
                CONTAINER_NAME="$2"
                shift 2
                ;;
            -i|--image)
                IMAGE_NAME="$2"
                shift 2
                ;;
            -k|--api-key)
                API_SECRET="$2"
                shift 2
                ;;
            -m|--mihomo-key)
                MIHOMO_SECRET="$2"
                shift 2
                ;;
            --ssl-port)
                SSL_PORT="$2"
                shift 2
                ;;
            --http-port)
                HTTP_PORT="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    print_success "Docker检查通过"
}

# 检查系统环境
check_system() {
    print_info "检查系统环境..."
    
    # 检查TUN模块
    if ! lsmod | grep -q tun; then
        print_warning "TUN模块未加载，尝试加载..."
        if sudo modprobe tun 2>/dev/null; then
            print_success "TUN模块加载成功"
        else
            print_warning "TUN模块加载失败，可能影响TUN模式功能"
        fi
    else
        print_success "TUN模块已加载"
    fi
    
    # 检查TUN设备
    if [ ! -c /dev/net/tun ]; then
        print_warning "TUN设备不存在，尝试创建..."
        sudo mkdir -p /dev/net
        sudo mknod /dev/net/tun c 10 200 2>/dev/null || true
    fi
    
    # 启用IP转发
    if [ "$(cat /proc/sys/net/ipv4/ip_forward)" != "1" ]; then
        print_info "启用IPv4转发..."
        echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward >/dev/null
    fi
    
    print_success "系统环境检查完成"
}

# 生成SSL证书
generate_ssl_cert() {
    print_info "生成SSL证书..."
    
    mkdir -p ./ssl
    
    # 获取本机IP
    LOCAL_IP=$(hostname -I | awk '{print $1}' || echo "*************")
    
    # 生成证书
    docker run --rm -v $(pwd)/ssl:/ssl alpine/openssl req -x509 -newkey rsa:2048 \
        -keyout /ssl/api-server.key -out /ssl/api-server.crt -days 3650 -nodes \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=ZFMI/OU=API/CN=ZFMI-API" \
        -addext "subjectAltName=DNS:localhost,DNS:api.local,IP:127.0.0.1,IP:${LOCAL_IP}" \
        2>/dev/null
    
    # 设置权限
    chmod 600 ./ssl/api-server.key
    chmod 644 ./ssl/api-server.crt
    
    print_success "SSL证书生成完成"
    print_info "证书文件: ./ssl/api-server.crt"
    print_info "私钥文件: ./ssl/api-server.key"
    print_info "支持访问: localhost, 127.0.0.1, ${LOCAL_IP}"
}

# 检查SSL证书
check_ssl_cert() {
    if [ "$SSL_ENABLED" = "true" ]; then
        if [ ! -f "./ssl/api-server.crt" ] || [ ! -f "./ssl/api-server.key" ]; then
            print_warning "SSL证书不存在"
            if [ "$GENERATE_SSL" = "true" ]; then
                generate_ssl_cert
            else
                print_error "请先生成SSL证书或使用 -g 选项自动生成"
                exit 1
            fi
        else
            print_success "SSL证书检查通过"
        fi
    fi
}

# 停止并删除现有容器
cleanup_container() {
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_info "停止现有容器: ${CONTAINER_NAME}"
        docker stop "${CONTAINER_NAME}" >/dev/null 2>&1 || true
        
        print_info "删除现有容器: ${CONTAINER_NAME}"
        docker rm "${CONTAINER_NAME}" >/dev/null 2>&1 || true
        
        print_success "容器清理完成"
    fi
}

# 拉取最新镜像
pull_image() {
    print_info "拉取最新镜像: ${IMAGE_NAME}"
    docker pull "${IMAGE_NAME}"
    print_success "镜像拉取完成"
}

# 构建Docker运行命令
build_docker_command() {
    local cmd="docker run -d"
    cmd+=" --name ${CONTAINER_NAME}"
    cmd+=" --network host"
    cmd+=" --restart unless-stopped"
    cmd+=" --privileged"
    cmd+=" --cap-add NET_ADMIN"
    cmd+=" --cap-add NET_RAW"
    cmd+=" --cap-add NET_BIND_SERVICE"
    cmd+=" --device /dev/net/tun:/dev/net/tun"
    cmd+=" --env TZ=Asia/Shanghai"
    cmd+=" --env API_SECRET=${API_SECRET}"
    cmd+=" --env MIHOMO_SECRET=${MIHOMO_SECRET}"
    cmd+=" --env SUPERVISOR_ENABLED=true"
    cmd+=" --env MIHOMO_LOG_LEVEL=info"
    cmd+=" --env LOG_LEVEL=info"

    # SSL配置
    if [ "$SSL_ENABLED" = "true" ]; then
        cmd+=" --env ENABLE_SSL=true"
        cmd+=" --env SSL_PORT=${SSL_PORT}"
        cmd+=" --volume $(pwd)/ssl:/app/ssl:ro"
    else
        cmd+=" --env ENABLE_SSL=false"
    fi

    # HTTP配置
    if [ "$HTTP_ENABLED" = "true" ]; then
        cmd+=" --env ENABLE_HTTP=true"
    else
        cmd+=" --env ENABLE_HTTP=false"
    fi

    # 系统挂载
    cmd+=" --volume /lib/modules:/lib/modules:ro"
    cmd+=" --volume /etc/resolv.conf:/etc/resolv.conf:ro"

    # 镜像名称
    cmd+=" ${IMAGE_NAME}"

    echo "$cmd"
}

# 部署容器
deploy_container() {
    print_info "部署容器: ${CONTAINER_NAME}"

    local docker_cmd=$(build_docker_command)

    if eval "$docker_cmd"; then
        print_success "容器部署成功"

        # 等待容器启动
        print_info "等待容器启动..."
        sleep 5

        # 检查容器状态
        if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
            print_success "容器运行正常"
            show_access_info
        else
            print_error "容器启动失败"
            print_info "查看日志: docker logs ${CONTAINER_NAME}"
            exit 1
        fi
    else
        print_error "容器部署失败"
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    local local_ip=$(hostname -I | awk '{print $1}' || echo "your-ip")

    echo ""
    print_success "部署完成！"
    echo ""
    echo -e "${CYAN}访问信息:${NC}"

    if [ "$HTTP_ENABLED" = "true" ]; then
        echo -e "  HTTP API:  ${GREEN}http://${local_ip}:${HTTP_PORT}/api/${NC}"
    fi

    if [ "$SSL_ENABLED" = "true" ]; then
        echo -e "  HTTPS API: ${GREEN}https://${local_ip}:${SSL_PORT}/api/${NC} ${YELLOW}(需接受证书)${NC}"
    fi

    echo -e "  mihomo API: ${GREEN}http://${local_ip}:11024/${NC}"
    echo -e "  代理端口:   ${GREEN}${local_ip}:10801${NC}"
    echo ""
    echo -e "${CYAN}管理命令:${NC}"
    echo -e "  查看日志: ${BLUE}docker logs -f ${CONTAINER_NAME}${NC}"
    echo -e "  停止服务: ${BLUE}docker stop ${CONTAINER_NAME}${NC}"
    echo -e "  重启服务: ${BLUE}docker restart ${CONTAINER_NAME}${NC}"
    echo ""

    if [ "$SSL_ENABLED" = "true" ]; then
        echo -e "${YELLOW}注意: 首次访问HTTPS时需要在浏览器中接受自签名证书警告${NC}"
        echo ""
    fi
}

# 主函数
main() {
    print_header
    echo ""

    # 解析命令行参数
    parse_args "$@"

    # 显示配置信息
    print_info "部署配置:"
    echo "  容器名称: ${CONTAINER_NAME}"
    echo "  镜像名称: ${IMAGE_NAME}"
    echo "  SSL启用: ${SSL_ENABLED}"
    echo "  HTTP启用: ${HTTP_ENABLED}"
    if [ "$SSL_ENABLED" = "true" ]; then
        echo "  HTTPS端口: ${SSL_PORT}"
    fi
    if [ "$HTTP_ENABLED" = "true" ]; then
        echo "  HTTP端口: ${HTTP_PORT}"
    fi
    echo ""

    # 检查环境
    check_docker
    check_system

    # 处理SSL证书
    check_ssl_cert

    # 清理现有容器
    if [ "$FORCE_RECREATE" = "true" ] || docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        cleanup_container
    fi

    # 拉取镜像
    pull_image

    # 部署容器
    deploy_container
}

# 运行主函数
main "$@"
