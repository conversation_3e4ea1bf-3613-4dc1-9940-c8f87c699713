<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 SSL连接测试工具</h1>
        <p>此工具用于测试ZFMI API的SSL连接功能</p>
        
        <div class="form-group">
            <label for="protocol">协议类型</label>
            <select id="protocol" onchange="updateUrl()">
                <option value="http">HTTP (不加密)</option>
                <option value="https">HTTPS (SSL加密)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="host">服务器地址</label>
            <input type="text" id="host" value="*************" placeholder="IP地址或域名" onchange="updateUrl()">
        </div>
        
        <div class="form-group">
            <label for="port">端口</label>
            <input type="number" id="port" value="12918" placeholder="端口号" onchange="updateUrl()">
        </div>
        
        <div class="form-group">
            <label for="apiUrl">完整API地址</label>
            <input type="text" id="apiUrl" readonly style="background-color: #f8f9fa;">
        </div>
        
        <div class="form-group">
            <label for="token">API令牌</label>
            <input type="password" id="token" value="jhxnb666" placeholder="输入API令牌">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="ignoreSsl"> 忽略SSL证书错误 (用于自签名证书)
            </label>
        </div>
        
        <button onclick="testConnection()">🔍 测试连接</button>
        <button onclick="testHealthEndpoint()">❤️ 测试健康检查</button>
        <button onclick="testStatusEndpoint()">📊 测试状态接口</button>
        
        <div id="result"></div>
    </div>

    <script>
        function updateUrl() {
            const protocol = document.getElementById('protocol').value;
            const host = document.getElementById('host').value.trim();
            const port = document.getElementById('port').value.trim();
            
            if (host && port) {
                const url = `${protocol}://${host}:${port}`;
                document.getElementById('apiUrl').value = url;
            }
        }
        
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
        }
        
        async function makeRequest(endpoint) {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const token = document.getElementById('token').value.trim();
            
            if (!apiUrl || !token) {
                throw new Error('请填写完整的API地址和令牌');
            }
            
            const url = `${apiUrl}${endpoint}`;
            const options = {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                mode: 'cors',
            };
            
            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        }
        
        async function testConnection() {
            showResult('🔄 正在测试基本连接...', 'info');
            
            try {
                const response = await fetch(document.getElementById('apiUrl').value);
                showResult(`✅ 基本连接成功！\n状态码: ${response.status}\n状态文本: ${response.statusText}`, 'success');
            } catch (error) {
                let errorMsg = `❌ 连接失败: ${error.message}`;
                
                if (error.message.includes('certificate') || error.message.includes('SSL')) {
                    errorMsg += '\n\n💡 提示: 这可能是SSL证书问题，请尝试：\n1. 启用"忽略SSL证书错误"选项\n2. 在浏览器中手动访问API地址并接受证书\n3. 使用有效的SSL证书';
                }
                
                showResult(errorMsg, 'error');
            }
        }
        
        async function testHealthEndpoint() {
            showResult('🔄 正在测试健康检查接口...', 'info');
            
            try {
                const result = await makeRequest('/health');
                showResult(`✅ 健康检查成功！\n响应数据: ${JSON.stringify(result, null, 2)}`, 'success');
            } catch (error) {
                showResult(`❌ 健康检查失败: ${error.message}`, 'error');
            }
        }
        
        async function testStatusEndpoint() {
            showResult('🔄 正在测试状态接口...', 'info');
            
            try {
                const result = await makeRequest('/api/mihomo/status');
                showResult(`✅ 状态接口成功！\n响应数据: ${JSON.stringify(result, null, 2)}`, 'success');
            } catch (error) {
                showResult(`❌ 状态接口失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时初始化URL
        document.addEventListener('DOMContentLoaded', function() {
            updateUrl();
        });
    </script>
</body>
</html>
